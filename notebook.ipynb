#%% md
# This is a sample Jupyter Notebook

Below is an example of a code cell. 
Put your cursor into the cell and press <PERSON><PERSON>+Enter to execute it and select the next one, or click 'Run Cell' button.

Press Double Shift to search everywhere for classes, files, tool windows, actions, and settings.

To learn more about Jupyter Notebooks in PyCharm, see [help](https://www.jetbrains.com/help/pycharm/ipython-notebook-support.html).
For an overview of PyCharm, go to Help -> Learn IDE features or refer to [our documentation](https://www.jetbrains.com/help/pycharm/getting-started.html).
#%%
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, udf , from_json, to_json
from datetime import datetime, timedelta
import string

spark = SparkSession.builder \
    .appName("spark-app-每日阈值") \
    .getOrCreate()

resultUrl = "***************************************"
resultUsername = "root"
resultPassword = "!QAZ2wsx1234"
resultTableName = "dwd_daily_report_threshold"

sourceUrl = "***************************************"
sourceUsername = "root"
sourcePassword = "!QAZ2wsx1234"
sourceTableName = "dwd_gxgx_gx_zhzx_mrjq"

target_jqbg = spark.read \
    .format("jdbc") \
    .option("url", resultUrl) \
    .option("dbtable", resultTableName) \
    .option("user", resultUsername) \
    .option("password", resultPassword) \
    .option("driver", "com.mysql.cj.jdbc.Driver") \
    .load()
target_jqbg.createOrReplaceTempView("target_jqbg")

dwd_gxgx_gx_zhzx_mrjq = spark.read \
    .format("jdbc") \
    .option("url", sourceUrl) \
    .option("dbtable", sourceTableName) \
    .option("user", sourceUsername) \
    .option("password", sourcePassword) \
    .option("driver", "com.mysql.cj.jdbc.Driver") \
    .load()
dwd_gxgx_gx_zhzx_mrjq.createOrReplaceTempView("dwd_gxgx_gx_zhzx_mrjq")

def get_search_time():
    begin_time_var='#{beginTime}'
    end_time_var='#{endTime}'
    #如果有统计范围参数，则用参数；如果没有则用代码里的
    begin_time_str = "2023-12-21 16:00:00" if "#" in begin_time_var else begin_time_var
    end_time_str = "2025-07-03 16:00:00" if "#" in end_time_var else end_time_var
    begin_time = datetime.strptime(begin_time_str, "%Y-%m-%d %H:%M:%S")
    end_time = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S")
    return (begin_time, end_time)

#按日生成统计时间列表
def split_time_by_day(begin_time: datetime, end_time: datetime):
    time_range = []
    current_time = begin_time
    while current_time <= end_time:
        time_range.append(current_time.strftime("%Y-%m-%d %H:%M:%S"))
        current_time += timedelta(days=1)
    return time_range

def create_daily_table(current_time: string):
    sql=f"""
-- 统计去年同期月每天平均数据量
WITH
-- 获取当前日期
current_day AS (
 --SELECT current_timestamp() AS today
 --  SELECT cast('2025-07-03 18:00:00' as timestamp) AS today
 SELECT from_utc_timestamp(cast('{current_time}' as timestamp),'Asia/Shanghai') AS today
),
-- 计算当月周期的开始和结束日期
current_period AS (
  SELECT
    CASE
      WHEN DAY((SELECT today FROM current_day)) >= 21 THEN
        -- 如果今天是21号或之后，当前周期是从本月（上月21号到本月20号）
        -- 获取上个月的21号
        DATE_ADD(ADD_MONTHS(TRUNC((SELECT today FROM current_day), 'MM'), -1), 20)
      ELSE
        -- 如果今天是20号或之前，当前周期是从上月（上上月21号到上月20号）
        -- 获取上上个月的21号
        DATE_ADD(ADD_MONTHS(TRUNC((SELECT today FROM current_day), 'MM'), -2), 20)
    END AS start_date,
    CASE
      WHEN DAY((SELECT today FROM current_day)) >= 21 THEN
        -- 如果今天是21号或之后，当前周期是到本月20号结束
        -- 获取本月的20号
        DATE_ADD(TRUNC((SELECT today FROM current_day), 'MM'), 19)
      ELSE
        -- 如果今天是20号或之前，当前周期是到上月20号结束
        -- 获取上个月的20号
        DATE_ADD(ADD_MONTHS(TRUNC((SELECT today FROM current_day), 'MM'), -1), 19)
    END AS end_date
  FROM current_day
),
-- 计算当天周期的开始和结束日期
current_time AS (
  SELECT
    CASE
      WHEN hour(today) >= 16 THEN
        -- 如果今天是16点之后
        concat(
            date_format(date_sub(to_date(today), 1), 'yyyy-MM-dd'),
            ' 16:00:00'
        )
      ELSE
        -- 如果今天是16点或之前
        concat(
            date_format(date_sub(to_date(today), 2), 'yyyy-MM-dd'),
            ' 16:00:00'
        )
    END AS start_time,
    CASE
      WHEN hour(today) >= 16 THEN
        -- 如果今天是16点或之后
         concat(
            date_format(to_date(today), 'yyyy-MM-dd'),
            ' 16:00:00'
        )
      ELSE
      -- 如果今天是16点或之前
        concat(
            date_format(date_sub(to_date(today), 1), 'yyyy-MM-dd'),
            ' 16:00:00'
        )
    END AS end_time
  FROM current_day
),
-- 计算去年同期月的开始和结束日期
last_year_period AS (
  SELECT
    add_months(start_date, -12) AS last_year_start,
    add_months(end_date, -12) AS last_year_end
  FROM current_period
),
-- 筛选去年同期月的数据并按天统计
filtered_data AS (
  SELECT
    jzgx as jzgx,
    count(*) AS daily_count
  FROM
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
    bjsj BETWEEN (SELECT last_year_start FROM last_year_period) AND (SELECT last_year_end FROM last_year_period)
    and jqlb in ('行政案件','刑事案件')
  group by jzgx
),
-- 计算平均每天数据量
day_avg_data AS (
SELECT '青松所' as jzgx, 1 AS last_year_avg_jq union all
SELECT '星辉所' as jzgx, 1 AS last_year_avg_jq union all
SELECT '翠竹所' as jzgx, 1 AS last_year_avg_jq union all
SELECT '红旗所' as jzgx, 1 AS last_year_avg_jq union all
SELECT '东湖所' as jzgx, 1 AS last_year_avg_jq union all
SELECT '松柏所' as jzgx, 1 AS last_year_avg_jq union all
SELECT '新港所' as jzgx, 1 AS last_year_avg_jq union all
SELECT '朝阳路所' as jzgx, 1 AS last_year_avg_jq union all
SELECT '阳光所' as jzgx, 1 AS last_year_avg_jq union all
SELECT '金山所' as jzgx, 1 AS last_year_avg_jq
),
today_jq as (
  SELECT
    jzgx as jzgx,
    count(*) as today_jq_count
  from
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
    bjsj BETWEEN (select cast(start_time as timestamp) from current_time) AND (select cast(end_time as timestamp) from current_time)
    and jqlb in ('行政案件','刑事案件')
  group by jzgx
)
SELECT
    md5(concat(jq.jzgx,cast((select DATE_FORMAT(today,  'yyyy-MM-dd') as today_d from current_day) as string))) as id,
    replace(replace((jq.jzgx), '高新', ''), '所', '') as dwmc,
    cast((select DATE_FORMAT(end_time,  'yyyy-MM-dd') as today_d from current_time) as string) as ssrq,
    (select cast(start_time as timestamp) from current_time) as kssj,
    (select cast(end_time as timestamp) from current_time) as jssj,
    jq.today_jq_count as today_jq_count,
    t.last_year_avg_jq as last_year_avg_jq,
    jq.today_jq_count - t.last_year_avg_jq as difference
  from today_jq jq
left join day_avg_data t on TRIM(t.jzgx) = TRIM(jq.jzgx)
where jq.jzgx is not null and not exists (select 1 from target_jqbg as f where md5(concat(jq.jzgx,cast((select DATE_FORMAT(today,  'yyyy-MM-dd') as today_d from current_day) as string))) = f.id)
-- where not exists (select 1 from target_jqbg as f where md5(concat(jq.jzgx,'2025-06-18')) = f.id)
"""
    rs=spark.sql(sql)
    rs.show()
    return rs


begin_time, end_time = get_search_time()
time_range = split_time_by_day(begin_time, end_time)
print(time_range)
for current_time in time_range:
    rs = create_daily_table(current_time)
    rs.write.format('jdbc').option("url", resultUrl).option("dbtable", resultTableName).option("user",resultUsername).option("password",resultPassword).option("driver", "com.mysql.cj.jdbc.Driver").mode("append").save()


spark.stop()
