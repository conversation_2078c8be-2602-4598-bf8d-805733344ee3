#%% md
# This is a sample Jupyter Notebook

Below is an example of a code cell. 
Put your cursor into the cell and press <PERSON>ft+Enter to execute it and select the next one, or click 'Run Cell' button.

Press Double Shift to search everywhere for classes, files, tool windows, actions, and settings.

To learn more about Jupyter Notebooks in PyCharm, see [help](https://www.jetbrains.com/help/pycharm/ipython-notebook-support.html).
For an overview of PyCharm, go to Help -> Learn IDE features or refer to [our documentation](https://www.jetbrains.com/help/pycharm/getting-started.html).
#%%
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, udf , from_json, to_json
from datetime import datetime, timedelta
import string

spark = SparkSession.builder \
    .appName("spark-app-每日警情报告") \
    .config("hive.metastore.uris", "thrift://***********:21088,thrift://***********:21088") \
    .enableHiveSupport() \
    .getOrCreate()

resultUrl = "****************************************"
resultUsername = "root"
resultPassword = "!QAZ2wsx1234"

target_jqbg = spark.read \
    .format("jdbc") \
    .option("url", resultUrl) \
    .option("dbtable", "dwd_dailyPaperData_gdw_wffz_gjqlx_mrtjsh") \
    .option("user", resultUsername) \
    .option("password", resultPassword) \
    .option("driver", "com.mysql.cj.jdbc.Driver") \
    .load()
target_jqbg.createOrReplaceTempView("target_jqbg")

sourceUrl = "****************************************"
sourceUsername = "root"
sourcePassword = "!QAZ2wsx1234"

dwd_gxgx_gx_zhzx_mrjq = spark.read \
    .format("jdbc") \
    .option("url", sourceUrl) \
    .option("dbtable", "dwd_gxgx_gx_zhzx_mrjq") \
    .option("user", sourceUsername) \
    .option("password", sourcePassword) \
    .option("driver", "com.mysql.cj.jdbc.Driver") \
    .load()
dwd_gxgx_gx_zhzx_mrjq.createOrReplaceTempView("dwd_gxgx_gx_zhzx_mrjq")

def get_search_time():
    begin_time_var='#{beginTime}'
    end_time_var='#{endTime}'
    #如果有统计范围参数，则用参数；如果没有则用代码里的
    begin_time_str = "2023-12-21 16:00:00" if "#" in begin_time_var else begin_time_var
    end_time_str = "2025-07-03 16:00:00" if "#" in end_time_var else end_time_var
    begin_time = datetime.strptime(begin_time_str, "%Y-%m-%d %H:%M:%S")
    end_time = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S")
    return (begin_time, end_time)

#按日生成统计时间列表
def split_time_by_day(begin_time: datetime, end_time: datetime):
    time_range = []
    current_time = begin_time
    while current_time <= end_time:
        time_range.append(current_time.strftime("%Y-%m-%d %H:%M:%S"))
        current_time += timedelta(days=1)
    return time_range

def create_daily_table(current_time: string):
    sql=f"""
-- 统计去年同期月每天平均数据量
WITH 
-- 获取当前日期
current_day AS (
  --SELECT current_timestamp() AS today
--  SELECT cast('2025-06-20 18:00:00' as timestamp) AS today
SELECT from_utc_timestamp(cast('{current_time}' as timestamp),'Asia/Shanghai') AS today
),                               
-- 计算当天周期的开始和结束日期
current_time AS (
  SELECT 
    CASE 
      WHEN hour(today) >= 16 THEN 
        -- 如果今天是16点之后
        concat(
            date_format(date_sub(to_date(today), 1), 'yyyy-MM-dd'),
            ' 16:00:00'
        )
      ELSE 
        -- 如果今天是16点或之前
        concat(
            date_format(date_sub(to_date(today), 2), 'yyyy-MM-dd'),
            ' 16:00:00'
        )
    END AS start_time,
    CASE 
      WHEN hour(today) >= 16 THEN 
        -- 如果今天是16点或之后
         concat(
            date_format(to_date(today), 'yyyy-MM-dd'),
            ' 16:00:00'
        )
      ELSE 
      -- 如果今天是16点或之前
        concat(
            date_format(date_sub(to_date(today), 1), 'yyyy-MM-dd'),
            ' 16:00:00'
        )
    END AS end_time
  FROM current_day
),
lq_jq as (
  SELECT
    jzgx as jzgx,
    count(*) as lq_count
  from
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
    bjsj BETWEEN (select cast(start_time as timestamp) from current_time) AND (select cast(end_time as timestamp) from current_time)
    and jqlb in ('行政案件','刑事案件') and jqlx in ('抢劫','抢夺','抢夺(行政案件)')
    and jmsh IS NOT NULL
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一个警%')
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一起警%')
  group by jzgx
),
pq_jq as (
  SELECT
    jzgx as jzgx,
    count(*) as pq_count
  from
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
    bjsj BETWEEN (select cast(start_time as timestamp) from current_time) AND (select cast(end_time as timestamp) from current_time)
    and jqlb in ('行政案件','刑事案件') and jqxl in ('扒窃(随窃)')
    and jmsh IS NOT NULL
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一个警%')
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一起警%')
  group by jzgx
),
cncw_jq as (
  SELECT
    jzgx as jzgx,
    count(*) as cncw_count
  from
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
    bjsj BETWEEN (select cast(start_time as timestamp) from current_time) AND (select cast(end_time as timestamp) from current_time)
    and jqlb in ('行政案件','刑事案件') and jqxl in ('车内财物')
    and jmsh IS NOT NULL
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一个警%')
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一起警%')
  group by jzgx
),
sc_jq as (
  SELECT
    jzgx as jzgx,
    count(*) as sc_count
  from
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
    bjsj BETWEEN (select cast(start_time as timestamp) from current_time) AND (select cast(end_time as timestamp) from current_time)
    and jqlb in ('行政案件','刑事案件') and jqxl in ('汽车','摩托车','电动自行车')
    and jmsh IS NOT NULL
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一个警%')
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一起警%')
  group by jzgx
),
rhdq_jq as (
  SELECT
    jzgx as jzgx,
    count(*) as rhdq_count
  from
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
    bjsj BETWEEN (select cast(start_time as timestamp) from current_time) AND (select cast(end_time as timestamp) from current_time)
    and jqlb in ('行政案件','刑事案件') and jqxl in ('入户盗窃')
    and jmsh IS NOT NULL
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一个警%')
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一起警%')
  group by jzgx
),
ybdq_jq as (
  SELECT
    jzgx as jzgx,
    count(*) as ybdq_count
  from
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
    bjsj BETWEEN (select cast(start_time as timestamp) from current_time) AND (select cast(end_time as timestamp) from current_time)
    and jqlb in ('行政案件','刑事案件') and jqlx in ('盗窃','盗窃(行政案件)') and jqxl not in ('扒窃(随窃)','车内财物','汽车','摩托车','电动自行车','入户盗窃')
    and jmsh IS NOT NULL
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一个警%')
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一起警%')
  group by jzgx
),
zdq_jq as (
  SELECT
    jzgx as jzgx,
    count(*) as zdq_count
  from
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
    bjsj BETWEEN (select cast(start_time as timestamp) from current_time) AND (select cast(end_time as timestamp) from current_time)
    and jqlb in ('行政案件','刑事案件') and jqlx in ('盗窃','盗窃(行政案件)')
    and jmsh IS NOT NULL
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一个警%')
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一起警%')
  group by jzgx
),
dxzp_jq as (
  SELECT
    jzgx as jzgx,
    count(*) as dxzp_count
  from
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
    bjsj BETWEEN (select cast(start_time as timestamp) from current_time) AND (select cast(end_time as timestamp) from current_time)
    and jqlb in ('行政案件','刑事案件') and jqxl in ('通讯诈骗')
    and jmsh IS NOT NULL
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一个警%')
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一起警%')
  group by jzgx
),
qtzp_jq as (
  SELECT
    jzgx as jzgx,
    count(*) as qtzp_count
  from
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
    bjsj BETWEEN (select cast(start_time as timestamp) from current_time) AND (select cast(end_time as timestamp) from current_time)
    and jqlb in ('行政案件','刑事案件') and jqlx in ('诈骗','诈骗(行政案件)') and jqxl not in ('通讯诈骗')
    and jmsh IS NOT NULL
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一个警%')
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一起警%')
  group by jzgx
),
zzp_jq as (
  SELECT
    jzgx as jzgx,
    count(*) as zzp_count
  from
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
    bjsj BETWEEN (select cast(start_time as timestamp) from current_time) AND (select cast(end_time as timestamp) from current_time)
    and jqlb in ('行政案件','刑事案件') and jqlx in ('诈骗','诈骗(行政案件)')
    and jmsh IS NOT NULL
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一个警%')
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一起警%')
  group by jzgx
),
odtr_jq as (
  SELECT
    jzgx as jzgx,
    count(*) as odtr_count
  from
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
    bjsj BETWEEN (select cast(start_time as timestamp) from current_time) AND (select cast(end_time as timestamp) from current_time)
    and jqlb in ('行政案件','刑事案件') and jqlx in ('殴打他人')
    and jmsh IS NOT NULL
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一个警%')
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一起警%')
  group by jzgx
),
jtbl_jq as (
  SELECT
    jzgx as jzgx,
    count(*) as jtbl_count
  from
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
    bjsj BETWEEN (select cast(start_time as timestamp) from current_time) AND (select cast(end_time as timestamp) from current_time)
    and jqlb in ('行政案件','刑事案件') and jqlx in ('家庭暴力')
    and jmsh IS NOT NULL
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一个警%')
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一起警%')
  group by jzgx
),
rlggzx_jq as (
  SELECT
    jzgx as jzgx,
    count(*) as rlggzx_count
  from
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
    bjsj BETWEEN (select cast(start_time as timestamp) from current_time) AND (select cast(end_time as timestamp) from current_time)
    and jqlb in ('行政案件','刑事案件') and jqlx in ('扰乱公共秩序')
    and jmsh IS NOT NULL
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一个警%')
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一起警%')
  group by jzgx
),
shgscc_jq as (
  SELECT
    jzgx as jzgx,
    count(*) as shgscc_count
  from
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
    bjsj BETWEEN (select cast(start_time as timestamp) from current_time) AND (select cast(end_time as timestamp) from current_time)
    and jqlb in ('行政案件','刑事案件') and jqlx in ('损坏公私财物')
    and jmsh IS NOT NULL
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一个警%')
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一起警%')
  group by jzgx
),
qt_jq as (
  SELECT
    jzgx as jzgx,
    count(*) as qtjq_count
  from
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
    bjsj BETWEEN (select cast(start_time as timestamp) from current_time) AND (select cast(end_time as timestamp) from current_time)
    and jqlb in ('行政案件','刑事案件') and jqlx not in ('盗窃','盗窃(行政案件)','诈骗','诈骗(行政案件)') and jqlx not in ('殴打他人','家庭暴力','扰乱公共秩序','损坏公私财物')
    and jmsh IS NOT NULL
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一个警%')
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一起警%')
  group by jzgx
),
sum_jq as (
  SELECT
    jzgx as jzgx,
    count(*) as sum_count
  from
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
    bjsj BETWEEN (select cast(start_time as timestamp) from current_time) AND (select cast(end_time as timestamp) from current_time)
    and jqlb in ('行政案件','刑事案件')
    and jmsh IS NOT NULL
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一个警%')
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一起警%')
  group by jzgx
),
dwmc_t as (
  SELECT
    jzgx as jzgx
  from
    dwd_gxgx_gx_zhzx_mrjq
  WHERE
  bjsj BETWEEN (select cast(start_time as timestamp) from current_time) AND (select cast(end_time as timestamp) from current_time)
    and jqlb in ('行政案件','刑事案件')
    and jmsh IS NOT NULL
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一个警%')
    and NOT (jmsh LIKE '%51010020%' AND jmsh LIKE '%一起警%')
  group by jzgx
)
SELECT
    md5(concat(dw.jzgx,cast((select DATE_FORMAT(today,  'yyyy-MM-dd') as today_d from current_day) as string))) as id,
    -- md5(concat(dw.jzgx,'2025-06-15')) as id,
     replace(replace(if(dw.jzgx is null,'0',dw.jzgx), '高新', ''), '所', '') as dwmc,
    if(lq.lq_count is null,'0',lq.lq_count) as lq_count,
    if(pq.pq_count is null,'0',pq.pq_count) as pq_count,
    if(cncw.cncw_count is null,'0',cncw.cncw_count) as cncw_count,
    if(sc.sc_count is null,'0',sc.sc_count) as sc_count,
    if(rhdq.rhdq_count is null,'0',rhdq.rhdq_count) as rhdq_count,
    if(ybdq.ybdq_count is null,'0',ybdq.ybdq_count) as ybdq_count,
    if(dxzp.dxzp_count is null,'0',dxzp.dxzp_count) as dxzp_count,
    if(qtzp.qtzp_count is null,'0',qtzp.qtzp_count) as qtzp_count,
    if(zzp.zzp_count is null,'0',zzp.zzp_count) as zzp_count,
    if(odtr.odtr_count is null,'0',odtr.odtr_count) as odtr_count,
    if(jtbl.jtbl_count is null,'0',jtbl.jtbl_count) as jtbl_count,
    if(rlggzx.rlggzx_count is null,'0',rlggzx.rlggzx_count) as rlggzx_count,
    if(shgscc.shgscc_count is null,'0',shgscc.shgscc_count) as shgscc_count,
    if(qt.qtjq_count is null,'0',qt.qtjq_count) as qtjq_count,
--    if((dxzp.dxzp_count + qtzp.qtzp_count) is null,'0',(dxzp.dxzp_count + qtzp.qtzp_count)) as zzp_count,
    if(zdq.zdq_count is null,'0',zdq.zdq_count) as zdq_count,
    if(sum.sum_count is null,'0',sum.sum_count) as sum_count,
    (select cast(start_time as timestamp) from current_time) as kssj, 
    (select cast(end_time as timestamp) from current_time) as jssj
  from dwmc_t dw
  left join lq_jq lq on lq.jzgx = dw.jzgx
  left join pq_jq pq on pq.jzgx = dw.jzgx
  left join cncw_jq cncw on cncw.jzgx = dw.jzgx
  left join sc_jq sc on sc.jzgx = dw.jzgx
  left join rhdq_jq rhdq on rhdq.jzgx = dw.jzgx
  left join ybdq_jq ybdq on ybdq.jzgx = dw.jzgx
  left join dxzp_jq dxzp on dxzp.jzgx = dw.jzgx
  left join qtzp_jq qtzp on qtzp.jzgx = dw.jzgx
  left join zzp_jq zzp on zzp.jzgx = dw.jzgx
  left join odtr_jq odtr on odtr.jzgx = dw.jzgx
  left join jtbl_jq jtbl on jtbl.jzgx = dw.jzgx
  left join rlggzx_jq rlggzx on rlggzx.jzgx = dw.jzgx
  left join shgscc_jq shgscc on shgscc.jzgx = dw.jzgx
  left join qt_jq qt on qt.jzgx = dw.jzgx
  left join zdq_jq zdq on zdq.jzgx = dw.jzgx
  left join sum_jq sum on sum.jzgx = dw.jzgx
where dw.jzgx is not null and  not exists (select 1 from target_jqbg as f where md5(concat(dw.jzgx,cast((select DATE_FORMAT(today,  'yyyy-MM-dd') as today_d from current_day) as string))) = f.id)
-- where not exists (select 1 from target_jqbg as f where md5(concat(dw.jzgx,'2025-06-16')) = f.id)
"""
    rs = spark.sql(sql)
    rs.show()
    return rs


resultUrl = "****************************************"
resultUsername = "root"
resultPassword = "!QAZ2wsx1234"

begin_time, end_time = get_search_time()
time_range = split_time_by_day(begin_time, end_time)
print(time_range)
temp_view = []
for current_time in time_range:
    rs = create_daily_table(current_time)
    rs.write.format('jdbc').option("url", resultUrl).option("dbtable", "dwd_dailyPaperData_gdw_wffz_gjqlx_mrtjsh").option("user",resultUsername).option("password",resultPassword).option("driver", "com.mysql.cj.jdbc.Driver").mode("append").save()


#union_df.write.format('jdbc').option("url", resultUrl).option("dbtable", "dwd_dailyPaperData_gdw_wffz_gjqlx_mrtjsh").option("user",resultUsername).option("password",resultPassword).option("driver", "com.mysql.cj.jdbc.Driver").mode("append").save()
spark.stop()